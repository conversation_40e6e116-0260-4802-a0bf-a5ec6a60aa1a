#!/usr/bin/env python3
"""
Script para estimativa de sinais de compra e venda usando LSTM
Baseado nas mesmas features do estimador XGBoost, importando de features_xgboost.py
Utiliza arquitetura LSTM para previsão sequencial de sinais binários (0=Venda, 1=<PERSON>mpra)
"""

import os
import sys
import random
import warnings
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping
import yfinance as yf

# Fixar seeds para reprodutibilidade
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)
tf.random.set_seed(RANDOM_SEED)
os.environ['PYTHONHASHSEED'] = str(RANDOM_SEED)

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment
from features_xgboost import calcular_features_e_sinais
from classificador_xgboost_sinais import carregar_acoes_diversificadas
warnings.filterwarnings('ignore')
# Usar a função do classificador XGBoost para manter consistência

def carregar_carteira_atual():
    """
    Carrega a carteira atual do arquivo CSV
    Retorna um dicionário com ticker: quantidade
    """
    try:
        file_paths = config.get_file_paths()
        carteira_path = file_paths['carteira']

        if not os.path.exists(carteira_path):
            print(f"   ⚠️ Arquivo de carteira não encontrado: {carteira_path}")
            return {}

        df_carteira = pd.read_csv(carteira_path)
        carteira = {}

        for _, row in df_carteira.iterrows():
            ticker = str(row['ticker']).strip()
            quantidade = int(row['quantidade'])

            # Apenas posições positivas (ações em carteira)
            if quantidade > 0:
                # Adicionar .SA se necessário
                if not ticker.endswith('.SA'):
                    ticker_sa = ticker + '.SA'
                else:
                    ticker_sa = ticker
                carteira[ticker_sa] = quantidade

        print(f"📋 Carteira atual carregada: {len(carteira)} posições ativas")
        for ticker, qtd in carteira.items():
            ticker_clean = ticker.replace('.SA', '')
            print(f"   • {ticker_clean}: {qtd} ações")

        return carteira

    except Exception as e:
        print(f"   ❌ Erro ao carregar carteira: {e}")
        return {}


def preparar_dados_lstm(dataset, feature_cols, target_col, sequence_length=20):
    """
    Prepara os dados para entrada no LSTM (sequências) para regressão
    Target: preço da ação no dia seguinte (Media_OHLC)
    """
    X = dataset[feature_cols]
    y = dataset[target_col]  # Media_OHLC para predição de preço

    X_seq, y_seq = [], []
    for i in range(len(X) - sequence_length):
        X_seq.append(X.iloc[i:(i + sequence_length)].values)
        y_seq.append(y.iloc[i + sequence_length])  # Preço do dia seguinte
    return np.array(X_seq), np.array(y_seq)


def treinar_lstm_regressao(X_train, y_train, X_test, y_test, input_shape, epochs=50, batch_size=32):
    """
    Treina um modelo LSTM para regressão (predição de preços)
    """
    model = Sequential()
    model.add(LSTM(config.get('lstm.units', 50), input_shape=input_shape, return_sequences=False))
    model.add(Dropout(config.get('lstm.dropout', 0.2)))
    model.add(Dense(config.get('lstm.dense_units', 25), activation='relu'))
    model.add(Dense(1))  # Output layer for regression (no activation)

    model.compile(optimizer='adam', loss='mean_squared_error', metrics=['mae'])

    early_stop = EarlyStopping(monitor='val_loss', patience=config.get('lstm.patience', 10), restore_best_weights=True)

    history = model.fit(
        X_train, y_train,
        validation_data=(X_test, y_test),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stop],
        verbose=1
    )

    return model, history





def carregar_carteira_atual():
    """
    Carrega a carteira atual do arquivo carteira.csv
    Retorna um dicionário com ticker -> quantidade atual
    """
    try:
        carteira_df = pd.read_csv('carteira.csv')

        # Calcular posição atual de cada ticker
        carteira_atual = {}
        for _, row in carteira_df.iterrows():
            ticker = row['ticker']
            quantidade = row['quantidade']

            if ticker in carteira_atual:
                carteira_atual[ticker] += quantidade
            else:
                carteira_atual[ticker] = quantidade

        # Filtrar apenas tickers com quantidade > 0
        carteira_atual = {ticker: qtd for ticker, qtd in carteira_atual.items() if qtd > 0}

        print(f"📋 Carteira atual carregada: {len(carteira_atual)} posições ativas")
        for ticker, qtd in carteira_atual.items():
            ticker_clean = ticker.replace('.SA', '')
            print(f"   • {ticker_clean}: {qtd} ações")

        return carteira_atual

    except FileNotFoundError:
        print(f"⚠️ Arquivo carteira.csv não encontrado - mostrando todos os sinais de venda")
        return {}
    except Exception as e:
        print(f"⚠️ Erro ao carregar carteira: {e} - mostrando todos os sinais de venda")
        return {}






def aplicar_predicoes_lstm(acoes_dados, model, scaler, feature_cols, sequence_length):
    """
    Aplica as predições do modelo LSTM aos dados de cada ação
    Gera sinais de compra/venda baseados na comparação entre preço atual e preço previsto
    """
    threshold = config.get('xgboost.features.pct_threshold', 0.4)  # Threshold para variação percentual
    acoes_com_predicoes = {}

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > sequence_length:
            dados_copy = dados.copy()

            # Verificar se todas as features existem
            features_disponiveis = [col for col in feature_cols if col in dados_copy.columns]
            if len(features_disponiveis) != len(feature_cols):
                print(f"     ⚠️ Features faltando para {ticker}: {len(features_disponiveis)}/{len(feature_cols)}")
                continue

            # Pegar os últimos 'sequence_length' dias para predição
            X_pred = dados_copy[feature_cols].tail(sequence_length)

            if len(X_pred) < sequence_length:
                continue  # Not enough data for sequence

            # Aplicar scaler
            X_scaled = scaler.transform(X_pred)
            X_scaled = X_scaled.reshape(1, sequence_length, len(feature_cols))  # Reshape for LSTM

            # Fazer predição do PREÇO
            preco_previsto = model.predict(X_scaled)[0][0]
            preco_atual = dados_copy['Media_OHLC'].iloc[-1]

            # Calcular variação percentual esperada
            if preco_atual > 0:
                variacao_pct = ((preco_previsto - preco_atual) / preco_atual) * 100
            else:
                variacao_pct = 0.0

            # Gerar sinais baseados na variação percentual
            if variacao_pct > threshold:
                sinal_compra = 1
                sinal_venda = 0
                sinal_tipo = "COMPRA"
            elif variacao_pct < -threshold:
                sinal_compra = 0
                sinal_venda = 1
                sinal_tipo = "VENDA"
            else:
                sinal_compra = 0
                sinal_venda = 0
                sinal_tipo = "SEM_SINAL"

            # Adicionar predições aos dados (apenas para o último dia)
            dados_copy.loc[dados_copy.index[-1], 'Preco_Previsto'] = preco_previsto
            dados_copy.loc[dados_copy.index[-1], 'Variacao_Pct_Prevista'] = variacao_pct
            dados_copy.loc[dados_copy.index[-1], 'Pred_Compra'] = sinal_compra
            dados_copy.loc[dados_copy.index[-1], 'Pred_Venda'] = sinal_venda
            dados_copy.loc[dados_copy.index[-1], 'Sinal_Tipo'] = sinal_tipo

            # Preencher NaN com valores padrão
            dados_copy['Preco_Previsto'] = dados_copy['Preco_Previsto'].fillna(0.0)
            dados_copy['Variacao_Pct_Prevista'] = dados_copy['Variacao_Pct_Prevista'].fillna(0.0)
            dados_copy['Pred_Compra'] = dados_copy['Pred_Compra'].fillna(0).astype(int)
            dados_copy['Pred_Venda'] = dados_copy['Pred_Venda'].fillna(0).astype(int)
            dados_copy['Sinal_Tipo'] = dados_copy['Sinal_Tipo'].fillna("SEM_SINAL")

            acoes_com_predicoes[ticker] = dados_copy

    print(f"   ✅ Predições aplicadas a {len(acoes_com_predicoes)} ações")
    print(f"   📊 Modelo prediz preço da ação no dia seguinte")
    print(f"   📊 Threshold utilizado: {threshold}% de variação")

    return acoes_com_predicoes


def criar_graficos_lstm(y_test, y_pred, rmse, mae, r2):
    """
    Cria gráficos de análise do modelo LSTM similar ao XGBoost
    """
    print(f"\n📊 Criando gráficos de análise do modelo LSTM...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lstm_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files', True):
        for arquivo in os.listdir(figures_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(figures_dir, arquivo))
        print(f"🗑️ Figuras antigas removidas de {figures_dir}")

    # Configurar estilo dos gráficos
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10

    # 1. Gráfico: Valores Reais vs Preditos (Scatter Plot)
    fig, ax = plt.subplots(figsize=(10, 8))

    # Scatter plot
    ax.scatter(y_test, y_pred, alpha=0.6, color='steelblue', s=20)

    # Linha de referência perfeita (y = x)
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Predição Perfeita')

    # Configurações do gráfico
    ax.set_xlabel('Preço Real (R$)', fontsize=12)
    ax.set_ylabel('Preço Predito (R$)', fontsize=12)
    ax.set_title('LSTM Regressor: Preços Reais vs Preditos\n' +
                f'R² = {r2:.4f} | RMSE = R$ {rmse:.2f} | MAE = R$ {mae:.2f}',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Adicionar estatísticas no gráfico
    ax.text(0.05, 0.95, f'Pontos: {len(y_test)}\nR²: {r2:.4f}\nRMSE: R$ {rmse:.2f}\nMAE: R$ {mae:.2f}',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'valores_reais_vs_preditos.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Gráfico: Distribuição dos Erros
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Calcular erros
    erros = y_pred - y_test
    erros_pct = ((y_pred - y_test) / y_test) * 100

    # Histograma dos erros absolutos
    ax1.hist(erros, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax1.set_xlabel('Erro (R$)', fontsize=12)
    ax1.set_ylabel('Frequência', fontsize=12)
    ax1.set_title('Distribuição dos Erros Absolutos\n' +
                 f'Média: R$ {erros.mean():.3f} | Desvio: R$ {erros.std():.3f}',
                 fontsize=12, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Histograma dos erros percentuais
    ax2.hist(erros_pct, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    ax2.axvline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax2.set_xlabel('Erro (%)', fontsize=12)
    ax2.set_ylabel('Frequência', fontsize=12)
    ax2.set_title('Distribuição dos Erros Percentuais\n' +
                 f'Média: {erros_pct.mean():.2f}% | Desvio: {erros_pct.std():.2f}%',
                 fontsize=12, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'distribuicao_erros.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 3. Gráfico: Q-Q Plot para normalidade dos resíduos
    fig, ax = plt.subplots(figsize=(8, 8))

    stats.probplot(erros, dist="norm", plot=ax)
    ax.set_title('Q-Q Plot: Normalidade dos Resíduos\n' +
                'Teste de Normalidade dos Erros do Modelo LSTM',
                fontsize=12, fontweight='bold')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'qq_plot_residuos.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 4. Gráfico: Série Temporal dos Últimos 200 Pontos
    fig, ax = plt.subplots(figsize=(14, 8))

    # Pegar últimos 200 pontos para visualização
    n_pontos = min(200, len(y_test))
    indices = range(len(y_test) - n_pontos, len(y_test))

    ax.plot(indices, y_test[-n_pontos:], 'o-', color='blue', linewidth=2,
            markersize=4, label='Preços Reais', alpha=0.8)
    ax.plot(indices, y_pred[-n_pontos:], 's-', color='red', linewidth=2,
            markersize=4, label='Preços Preditos', alpha=0.8)

    ax.set_xlabel('Índice da Amostra', fontsize=12)
    ax.set_ylabel('Preço (R$)', fontsize=12)
    ax.set_title(f'LSTM Regressor: Série Temporal (Últimos {n_pontos} Pontos)\n' +
                f'Comparação entre Preços Reais e Preditos', fontsize=14, fontweight='bold')
    ax.legend(loc='upper left')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'serie_temporal_predicoes.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 5. Gráfico: Resíduos vs Valores Preditos
    fig, ax = plt.subplots(figsize=(10, 8))

    ax.scatter(y_pred, erros, alpha=0.6, color='green', s=20)
    ax.axhline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax.set_xlabel('Valores Preditos (R$)', fontsize=12)
    ax.set_ylabel('Resíduos (R$)', fontsize=12)
    ax.set_title('Resíduos vs Valores Preditos\n' +
                'Análise de Homocedasticidade do Modelo LSTM',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'residuos_vs_preditos.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 6. Gráfico: Boxplot dos Erros por Faixa de Preço
    fig, ax = plt.subplots(figsize=(12, 8))

    # Criar faixas de preço
    faixas = pd.cut(y_test, bins=5, labels=['Muito Baixo', 'Baixo', 'Médio', 'Alto', 'Muito Alto'])
    dados_boxplot = [erros[faixas == faixa] for faixa in faixas.categories]

    ax.boxplot(dados_boxplot, labels=faixas.categories)
    ax.axhline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax.set_xlabel('Faixa de Preço', fontsize=12)
    ax.set_ylabel('Erro (R$)', fontsize=12)
    ax.set_title('Distribuição dos Erros por Faixa de Preço\n' +
                'Análise da Performance do Modelo por Segmento',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'erros_por_faixa_preco.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ Gráficos salvos em: {figures_dir}")
    print(f"   📊 6 gráficos de análise criados:")
    print(f"   • valores_reais_vs_preditos.png")
    print(f"   • distribuicao_erros.png")
    print(f"   • qq_plot_residuos.png")
    print(f"   • serie_temporal_predicoes.png")
    print(f"   • residuos_vs_preditos.png")
    print(f"   • erros_por_faixa_preco.png")


def imprimir_recomendacoes_lstm(acoes_com_predicoes):
    """
    Imprime recomendações de compra e venda baseadas no LSTM
    Sinais de venda apenas para ações na carteira atual
    Baseado na predição de preços do modelo LSTM
    """
    print(f"\n📊 ESTRATÉGIA DE TRADING - REGRESSOR LSTM")
    print(f"=" * 60)

    # Verificar data mais recente disponível
    data_mais_recente = None
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultima_data = dados.index.max()
            if data_mais_recente is None or ultima_data > data_mais_recente:
                data_mais_recente = ultima_data

    if data_mais_recente:
        print(f"� Análise baseada nos dados até: {data_mais_recente.strftime('%d/%m/%Y (%A)')}")
        print(f"🤖 Sinais baseados na predição de preços do modelo LSTM (regressão)")
        print(f"🎯 Threshold: {config.get('xgboost.features.pct_threshold', 0.4)}% de variação")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Coletar sinais
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ticker_clean = ticker.replace('.SA', '')
            ultimo_dia = dados.tail(1).iloc[0]

            # Verificar se há sinais
            if ultimo_dia.get('Pred_Compra', 0) == 1:
                sinais_compra.append({
                    'ticker_clean': ticker_clean,
                    'nome': ticker_clean,  # Nome simplificado
                    'preco': ultimo_dia.get('Media_OHLC', 0),
                    'preco_previsto': ultimo_dia.get('Preco_Previsto', 0),
                    'variacao_pct_prevista': ultimo_dia.get('Variacao_Pct_Prevista', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0),
                    'spread': ultimo_dia.get('Spread', 0),
                    'data': ultimo_dia.name
                })
            elif ultimo_dia.get('Pred_Venda', 0) == 1:
                # FILTRO: Apenas mostrar sinais de venda para ações na carteira
                if ticker in carteira_atual and carteira_atual[ticker] > 0:
                    sinais_venda.append({
                        'ticker_clean': ticker_clean,
                        'nome': ticker_clean,  # Nome simplificado
                        'preco': ultimo_dia.get('Media_OHLC', 0),
                        'preco_previsto': ultimo_dia.get('Preco_Previsto', 0),
                        'variacao_pct_prevista': ultimo_dia.get('Variacao_Pct_Prevista', 0),
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'quantidade_carteira': carteira_atual[ticker]
                    })

    # Exibir sinais de venda PRIMEIRO (ordenados por maior queda prevista)
    # APENAS para ações na carteira atual
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações na carteira):")
        print("-" * 60)
        # Ordenar por maior queda prevista (mais negativo primeiro)
        for sinal in sorted(sinais_venda, key=lambda x: x['variacao_pct_prevista']):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            quantidade = sinal.get('quantidade_carteira', 0)
            print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]}) - {quantidade} ações na carteira")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Preço previsto: R$ {sinal['preco_previsto']:.2f} ({sinal['variacao_pct_prevista']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 LSTM prevê queda no preço")
            print()
    else:
        print(f"\n🔴 SINAIS DE VENDA: Nenhuma ação na carteira com sinal de venda")
        print("-" * 60)

    # Exibir sinais de compra DEPOIS (ordenados por maior alta prevista)
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        # Ordenar por maior alta prevista (mais positivo primeiro)
        for sinal in sorted(sinais_compra, key=lambda x: x['variacao_pct_prevista'], reverse=True):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            print(f"   📈 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Preço previsto: R$ {sinal['preco_previsto']:.2f} ({sinal['variacao_pct_prevista']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 LSTM prevê alta no preço")
            print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🔴 Venda: {len(sinais_venda)} ações na carteira (ordenadas por maior queda prevista)")
    print(f"   🟢 Compra: {len(sinais_compra)} ações (ordenadas por maior alta prevista)")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")
    print(f"   🎯 Threshold: {config.get('xgboost.features.pct_threshold', 0.4)}% de variação")
    if carteira_atual:
        print(f"   📋 Carteira atual: {len(carteira_atual)} posições ativas")


def main():
    setup_environment()

    print("🤖 REGRESSOR LSTM - PREDIÇÃO DE PREÇOS")
    print("=" * 80)
    print("📊 Baseado nas mesmas features do classificador XGBoost")
    print("🎯 Modelo de regressão: prediz preço da ação no dia seguinte")

    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    data_period = config.get('xgboost.data_period')

    print(f"🎯 Sinais: Compra/Venda baseados em {signal_horizon} dias à frente")
    print(f"🔧 Features básicas: pct_change da Média OHLC passada ({ohlc_lags} dias), Volume, Spread, Volatilidade, dia da semana, mês")
    print(f"🔬 Features econométricas: Volatilidade de Parkinson, MFI, EMV, Amihud, Roll Spread,")
    print(f"   Hurst, Volume, CMF, A/D Line, Volume Oscillator")
    print(f"📅 Período de dados: {data_period}")
    print("=" * 80)

    # Carregar lista de ações diversificadas
    acoes = carregar_acoes_diversificadas()
    periodo = config.get('xgboost.data_period', '5y')

    print(f"\n📥 Baixando dados de {len(acoes)} ações...")
    acoes_dados = {}
    for ticker, nome in acoes:
        print(f"   • {ticker} ({nome})")
        dados = yf.download(ticker, period=periodo, progress=False)
        if dados is None or len(dados) == 0:
            print(f"     ❌ Falha ao baixar dados de {ticker}")
            continue
        # Padronizar nomes das colunas
        colunas_esperadas = ['Open', 'High', 'Low', 'Close', 'Volume']
        def normalizar_nome_col(col):
            nome_col = col[0] if isinstance(col, tuple) else col
            if isinstance(nome_col, str) and nome_col.lower() in [c.lower() for c in colunas_esperadas]:
                return nome_col.capitalize()
            return nome_col
        dados.columns = [normalizar_nome_col(col) for col in dados.columns]
        # Calcular features e sinais
        dados = calcular_features_e_sinais(dados, ticker=ticker)
        print(f"     ➡️ Registros após cálculo de features: {len(dados)}")
        if len(dados) == 0:
            print(f"     ⚠️ Nenhum registro válido após cálculo de features para {ticker}")
        acoes_dados[ticker] = dados

    print(f"✅ Processadas {len(acoes_dados)} ações com sucesso")

    # Unificar todos os dados em um único DataFrame
    print(f"\n🔧 Preparando dataset combinado para treinamento...")
    todos_dfs = []
    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            dados = dados.copy()
            dados['Ticker'] = ticker
            todos_dfs.append(dados)
    if not todos_dfs:
        print("❌ Nenhum dado disponível para treinamento.")
        return
    dataset = pd.concat(todos_dfs, ignore_index=True)

    # Selecionar features e target
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')
    weekday_features = ['Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta']
    month_features = [f'Mes_{i}' for i in range(1, 13)]
    quarter_features = ['Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter']
    holiday_features = ['Pre_Feriado_Brasil']
    basic_features = [f'Media_OHLC_PctChange_Lag_{i}' for i in range(1, ohlc_lags + 1)] + weekday_features + month_features + quarter_features + holiday_features
    econometric_features_all = [
        'Volume', 'Spread', 'Volatilidade',
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
        'High_Max_50', 'Low_Min_50'
    ]
    econometric_features_lagged = []
    for feature in econometric_features_all:
        for i in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{i}')
    feature_cols = basic_features + econometric_features_lagged
    feature_cols = [col for col in feature_cols if col in dataset.columns]
    target_col = 'Media_OHLC'  # Predizer preço da ação

    print(f"📊 Features selecionadas: {len(feature_cols)}")
    print(f"📊 Target: {target_col} (preço da ação)")

    # Remover NaN
    dataset_original = dataset.copy()
    dataset = dataset.dropna(subset=feature_cols + [target_col])
    print(f"📊 Registros após remoção de NaN: {len(dataset)} (removidos: {len(dataset_original) - len(dataset)})")

    # Divisão temporal (80% treino, 20% teste)
    test_size = config.get('xgboost.test_size', 0.2)
    split_idx = int(len(dataset) * (1 - test_size))
    train_df = dataset.iloc[:split_idx].copy()
    test_df = dataset.iloc[split_idx:].copy()

    # Normalizar features (CORREÇÃO: fit apenas no treino)
    scaler = StandardScaler()
    train_df[feature_cols] = scaler.fit_transform(train_df[feature_cols])
    test_df[feature_cols] = scaler.transform(test_df[feature_cols])

    # Resetar índices para a preparação de sequências
    train_df = train_df.reset_index(drop=True)
    test_df = test_df.reset_index(drop=True)

    # Preparar dados para LSTM
    sequence_length = config.get('lstm.sequence_length', 20)
    X_train, y_train = preparar_dados_lstm(train_df, feature_cols, target_col, sequence_length)
    X_test, y_test = preparar_dados_lstm(test_df, feature_cols, target_col, sequence_length)

    print(f"\n📊 Dados para LSTM:")
    print(f"   • Treino: {X_train.shape[0]} sequências")
    print(f"   • Teste: {X_test.shape[0]} sequências")
    print(f"   • Features: {len(feature_cols)}")
    print(f"   • Sequence length: {sequence_length}")

    # Estatísticas do target (preços)
    print(f"\nEstatísticas do target (preços) no treino:")
    print(f"   • Preço mínimo: R$ {y_train.min():.2f}")
    print(f"   • Preço máximo: R$ {y_train.max():.2f}")
    print(f"   • Preço médio: R$ {y_train.mean():.2f}")
    print(f"   • Desvio padrão: R$ {y_train.std():.2f}")

    # Treinar modelo LSTM
    print("\n🚀 Treinando modelo LSTM para regressão...")
    model, _ = treinar_lstm_regressao(
        X_train, y_train, X_test, y_test, input_shape=(sequence_length, len(feature_cols)),
        epochs=config.get('lstm.epochs', 50),
        batch_size=config.get('lstm.batch_size', 32)
    )

    # Avaliar modelo
    print("\n📊 Avaliando modelo...")
    y_pred = model.predict(X_test).flatten()

    # Calcular métricas de regressão
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print(f"\n🎯 Métricas de regressão no conjunto de teste:")
    print(f"   • RMSE: R$ {rmse:.4f}")
    print(f"   • MAE: R$ {mae:.4f}")
    print(f"   • R²: {r2:.4f}")

    # Estatísticas dos preços no teste
    print(f"\nEstatísticas dos preços no teste:")
    print(f"   • Preço real mínimo: R$ {y_test.min():.2f}")
    print(f"   • Preço real máximo: R$ {y_test.max():.2f}")
    print(f"   • Preço real médio: R$ {y_test.mean():.2f}")
    print(f"   • Preço previsto mínimo: R$ {y_pred.min():.2f}")
    print(f"   • Preço previsto máximo: R$ {y_pred.max():.2f}")
    print(f"   • Preço previsto médio: R$ {y_pred.mean():.2f}")

    # Criar gráficos de análise
    criar_graficos_lstm(y_test, y_pred, rmse, mae, r2)

    # Aplicar predições aos dados das ações
    print("\n🔮 Aplicando predições aos dados das ações...")
    acoes_com_predicoes = aplicar_predicoes_lstm(acoes_dados, model, scaler, feature_cols, sequence_length)

    # Imprimir recomendações
    imprimir_recomendacoes_lstm(acoes_com_predicoes)

    print(f"\n✅ Análise LSTM concluída!")
    print(f"📊 Modelo treinado com {len(feature_cols)} features")
    print(f"🎯 R² final: {r2:.3f}")
    print(f"📊 RMSE final: R$ {rmse:.4f}")


if __name__ == "__main__":
    main()
